package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// CallbackRepository 回调数据仓库接口
type CallbackRepository interface {
	// 用户回调配置管理
	GetUserCallbackConfig(ctx context.Context, userID string) (*model.UserCallbackConfig, error)
	SaveUserCallbackConfig(ctx context.Context, config *model.UserCallbackConfig) error
	UpdateUserCallbackConfig(ctx context.Context, config *model.UserCallbackConfig) error
	DeleteUserCallbackConfig(ctx context.Context, userID string) error

	// 回调记录管理
	SaveCallbackRecord(ctx context.Context, record *model.UnifiedCallbackRecord) error
	GetCallbackRecord(ctx context.Context, id uuid.UUID) (*model.UnifiedCallbackRecord, error)
	UpdateCallbackRecord(ctx context.Context, record *model.UnifiedCallbackRecord) error
	GetCallbackRecordsByOrderNo(ctx context.Context, orderNo string) ([]*model.UnifiedCallbackRecord, error)
	GetCallbackRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.UnifiedCallbackRecord, error)
	GetCallbackRecordsCount(ctx context.Context, userID string) (int64, error)

	// 转发记录管理
	SaveForwardRecord(ctx context.Context, record *model.CallbackForwardRecord) error
	GetForwardRecord(ctx context.Context, id uuid.UUID) (*model.CallbackForwardRecord, error)
	UpdateForwardRecord(ctx context.Context, record *model.CallbackForwardRecord) error
	GetPendingForwardRecords(ctx context.Context, limit int) ([]*model.CallbackForwardRecord, error)
	GetForwardRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.CallbackForwardRecord, error)
	GetForwardRecordsCountByUserID(ctx context.Context, userID string) (int64, error)
	GetForwardRecordsByCallbackID(ctx context.Context, callbackRecordID uuid.UUID) ([]*model.CallbackForwardRecord, error)

	// 🔥 新增：工单回调转发记录管理
	SaveWorkOrderForwardRecord(ctx context.Context, record *model.WorkOrderForwardRecord) error
	UpdateWorkOrderForwardRecord(ctx context.Context, record *model.WorkOrderForwardRecord) error
	GetWorkOrderForwardRecord(ctx context.Context, id uuid.UUID) (*model.WorkOrderForwardRecord, error)
	GetFailedWorkOrderForwardRecords(ctx context.Context, maxRetries int) ([]*model.WorkOrderForwardRecord, error)
	GetWorkOrderForwardRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.WorkOrderForwardRecord, error)
	GetWorkOrderForwardRecordsCount(ctx context.Context, userID string) (int64, error)
	GetWorkOrderForwardRecordsByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]*model.WorkOrderForwardRecord, error)

	// 🔥 新增：高性能增强转发记录查询
	GetEnhancedForwardRecordsWithJoin(ctx context.Context, userID string, limit, offset int, filters map[string]string) ([]*model.EnhancedForwardRecord, error)
	GetEnhancedForwardRecordsWithJoinOptimized(ctx context.Context, userID string, limit, offset int, filters map[string]string) ([]*model.EnhancedForwardRecord, error)
	GetEnhancedForwardRecordsCountWithFilters(ctx context.Context, userID string, filters map[string]string) (int64, error)
	GetEnhancedForwardRecordsCountWithFiltersOptimized(ctx context.Context, userID string, filters map[string]string) (int64, error)

	// 🔥 新增：游标分页查询（最高性能）
	GetEnhancedForwardRecordsWithCursor(ctx context.Context, userID string, limit int, cursor string, filters map[string]string) ([]*model.EnhancedForwardRecord, string, error)

	// 🔥 新增：重试记录管理
	SaveRetryRecord(ctx context.Context, record *model.CallbackRetryRecord) error
	GetRetryRecord(ctx context.Context, id uuid.UUID) (*model.CallbackRetryRecord, error)
	UpdateRetryRecord(ctx context.Context, record *model.CallbackRetryRecord) error
	GetPendingRetryRecords(ctx context.Context, limit int) ([]*model.CallbackRetryRecord, error)
	GetRetryRecordsByForwardRecordID(ctx context.Context, forwardRecordID uuid.UUID) ([]*model.CallbackRetryRecord, error)
	GetRetryRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.CallbackRetryRecord, error)
	GetRetryRecordsCount(ctx context.Context, userID string) (int64, error)
	CancelPendingRetryRecords(ctx context.Context, forwardRecordID uuid.UUID) error

	// 统计查询
	GetCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error)
	GetForwardRecordStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error)
	GetAdminCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time, provider, eventType string) (map[string]interface{}, error)
	// 🔥 新增：工单回调统计
	GetWorkOrderCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error)
}

// PostgresCallbackRepository PostgreSQL回调数据仓库实现
type PostgresCallbackRepository struct {
	db *sql.DB
}

// NewPostgresCallbackRepository 创建PostgreSQL回调数据仓库
func NewPostgresCallbackRepository(db *sql.DB) CallbackRepository {
	return &PostgresCallbackRepository{db: db}
}

// GetUserCallbackConfig 获取用户回调配置
func (r *PostgresCallbackRepository) GetUserCallbackConfig(ctx context.Context, userID string) (*model.UserCallbackConfig, error) {
	query := `
		SELECT id, user_id, callback_url, callback_secret, enabled, retry_count,
		       timeout_seconds, subscribed_events, created_at, updated_at
		FROM user_callback_configs
		WHERE user_id = $1
	`

	var config model.UserCallbackConfig
	err := r.db.QueryRowContext(ctx, query, userID).Scan(
		&config.ID,
		&config.UserID,
		&config.CallbackURL,
		&config.CallbackSecret,
		&config.Enabled,
		&config.RetryCount,
		&config.TimeoutSeconds,
		pq.Array(&config.SubscribedEvents),
		&config.CreatedAt,
		&config.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取用户回调配置失败: %w", err)
	}

	return &config, nil
}

// SaveUserCallbackConfig 保存用户回调配置
func (r *PostgresCallbackRepository) SaveUserCallbackConfig(ctx context.Context, config *model.UserCallbackConfig) error {
	if config.ID == uuid.Nil {
		config.ID = uuid.New()
	}
	config.CreatedAt = util.NowBeijing()
	config.UpdatedAt = util.NowBeijing()

	query := `
		INSERT INTO user_callback_configs (
			id, user_id, callback_url, callback_secret, enabled, retry_count,
			timeout_seconds, subscribed_events, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		ON CONFLICT (user_id) DO UPDATE SET
			callback_url = EXCLUDED.callback_url,
			callback_secret = EXCLUDED.callback_secret,
			enabled = EXCLUDED.enabled,
			retry_count = EXCLUDED.retry_count,
			timeout_seconds = EXCLUDED.timeout_seconds,
			subscribed_events = EXCLUDED.subscribed_events,
			updated_at = EXCLUDED.updated_at
	`

	_, err := r.db.ExecContext(ctx, query,
		config.ID,
		config.UserID,
		config.CallbackURL,
		config.CallbackSecret,
		config.Enabled,
		config.RetryCount,
		config.TimeoutSeconds,
		pq.Array(config.SubscribedEvents),
		config.CreatedAt,
		config.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("保存用户回调配置失败: %w", err)
	}

	return nil
}

// UpdateUserCallbackConfig 更新用户回调配置
func (r *PostgresCallbackRepository) UpdateUserCallbackConfig(ctx context.Context, config *model.UserCallbackConfig) error {
	config.UpdatedAt = util.NowBeijing()

	query := `
		UPDATE user_callback_configs SET
			callback_url = $1,
			callback_secret = $2,
			enabled = $3,
			retry_count = $4,
			timeout_seconds = $5,
			subscribed_events = $6,
			updated_at = $7
		WHERE user_id = $8
	`

	result, err := r.db.ExecContext(ctx, query,
		config.CallbackURL,
		config.CallbackSecret,
		config.Enabled,
		config.RetryCount,
		config.TimeoutSeconds,
		pq.Array(config.SubscribedEvents),
		config.UpdatedAt,
		config.UserID,
	)

	if err != nil {
		return fmt.Errorf("更新用户回调配置失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("用户回调配置不存在")
	}

	return nil
}

// DeleteUserCallbackConfig 删除用户回调配置
func (r *PostgresCallbackRepository) DeleteUserCallbackConfig(ctx context.Context, userID string) error {
	query := `DELETE FROM user_callback_configs WHERE user_id = $1`

	result, err := r.db.ExecContext(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("删除用户回调配置失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("用户回调配置不存在")
	}

	return nil
}

// SaveCallbackRecord 保存回调记录
func (r *PostgresCallbackRepository) SaveCallbackRecord(ctx context.Context, record *model.UnifiedCallbackRecord) error {
	if record.ID == uuid.Nil {
		record.ID = uuid.New()
	}
	record.CreatedAt = util.NowBeijing()
	record.UpdatedAt = util.NowBeijing()
	record.ReceivedAt = util.NowBeijing()

	query := `
		INSERT INTO unified_callback_records (
			id, provider, callback_type, order_no, customer_order_no, tracking_no, user_id,
			raw_data, raw_signature, standardized_data, event_type, internal_status, external_status,
			received_at, internal_error, external_error, retry_count, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ID,
		record.Provider,
		record.CallbackType,
		record.OrderNo,
		record.CustomerOrderNo,
		record.TrackingNo,
		record.UserID,
		record.RawData,
		record.RawSignature,
		record.StandardizedData,
		record.EventType,
		record.InternalStatus,
		record.ExternalStatus,
		record.ReceivedAt,
		record.InternalError,
		record.ExternalError,
		record.RetryCount,
		record.CreatedAt,
		record.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("保存回调记录失败: %w", err)
	}

	return nil
}

// GetCallbackRecord 获取回调记录
func (r *PostgresCallbackRepository) GetCallbackRecord(ctx context.Context, id uuid.UUID) (*model.UnifiedCallbackRecord, error) {
	query := `
		SELECT id, provider, callback_type, order_no, customer_order_no, tracking_no, user_id,
		       raw_data, raw_signature, standardized_data, event_type, internal_status, external_status,
		       received_at, internal_processed_at, external_processed_at, internal_error, external_error,
		       retry_count, created_at, updated_at
		FROM unified_callback_records
		WHERE id = $1
	`

	var record model.UnifiedCallbackRecord
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&record.ID,
		&record.Provider,
		&record.CallbackType,
		&record.OrderNo,
		&record.CustomerOrderNo,
		&record.TrackingNo,
		&record.UserID,
		&record.RawData,
		&record.RawSignature,
		&record.StandardizedData,
		&record.EventType,
		&record.InternalStatus,
		&record.ExternalStatus,
		&record.ReceivedAt,
		&record.InternalProcessedAt,
		&record.ExternalProcessedAt,
		&record.InternalError,
		&record.ExternalError,
		&record.RetryCount,
		&record.CreatedAt,
		&record.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("回调记录不存在")
		}
		return nil, fmt.Errorf("获取回调记录失败: %w", err)
	}

	return &record, nil
}

// UpdateCallbackRecord 更新回调记录
func (r *PostgresCallbackRepository) UpdateCallbackRecord(ctx context.Context, record *model.UnifiedCallbackRecord) error {
	record.UpdatedAt = util.NowBeijing()

	query := `
		UPDATE unified_callback_records SET
			standardized_data = $1,
			event_type = $2,
			internal_status = $3,
			external_status = $4,
			internal_processed_at = $5,
			external_processed_at = $6,
			internal_error = $7,
			external_error = $8,
			retry_count = $9,
			updated_at = $10
		WHERE id = $11
	`

	_, err := r.db.ExecContext(ctx, query,
		record.StandardizedData,
		record.EventType,
		record.InternalStatus,
		record.ExternalStatus,
		record.InternalProcessedAt,
		record.ExternalProcessedAt,
		record.InternalError,
		record.ExternalError,
		record.RetryCount,
		record.UpdatedAt,
		record.ID,
	)

	if err != nil {
		return fmt.Errorf("更新回调记录失败: %w", err)
	}

	return nil
}

// GetCallbackRecordsByOrderNo 根据订单号获取回调记录
func (r *PostgresCallbackRepository) GetCallbackRecordsByOrderNo(ctx context.Context, orderNo string) ([]*model.UnifiedCallbackRecord, error) {
	query := `
		SELECT id, provider, callback_type, order_no, customer_order_no, tracking_no, user_id,
		       raw_data, raw_signature, standardized_data, event_type, internal_status, external_status,
		       received_at, internal_processed_at, external_processed_at, internal_error, external_error,
		       retry_count, created_at, updated_at
		FROM unified_callback_records
		WHERE order_no = $1
		ORDER BY received_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询回调记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.UnifiedCallbackRecord
	for rows.Next() {
		var record model.UnifiedCallbackRecord
		err := rows.Scan(
			&record.ID,
			&record.Provider,
			&record.CallbackType,
			&record.OrderNo,
			&record.CustomerOrderNo,
			&record.TrackingNo,
			&record.UserID,
			&record.RawData,
			&record.RawSignature,
			&record.StandardizedData,
			&record.EventType,
			&record.InternalStatus,
			&record.ExternalStatus,
			&record.ReceivedAt,
			&record.InternalProcessedAt,
			&record.ExternalProcessedAt,
			&record.InternalError,
			&record.ExternalError,
			&record.RetryCount,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描回调记录失败: %w", err)
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetCallbackRecordsByUserID 根据用户ID获取回调记录（管理员可传空userID查询所有记录）
func (r *PostgresCallbackRepository) GetCallbackRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.UnifiedCallbackRecord, error) {
	var query string
	var args []interface{}

	if userID == "" {
		// 管理员查询所有记录，关联用户表获取用户名
		query = `
			SELECT ucr.id, ucr.provider, ucr.callback_type, ucr.order_no, ucr.customer_order_no, ucr.tracking_no, ucr.user_id,
			       ucr.raw_data, ucr.raw_signature, ucr.standardized_data, ucr.event_type, ucr.internal_status, ucr.external_status,
			       ucr.received_at, ucr.internal_processed_at, ucr.external_processed_at, ucr.internal_error, ucr.external_error,
			       ucr.retry_count, ucr.created_at, ucr.updated_at,
			       COALESCE(u.username, '') as username
			FROM unified_callback_records ucr
			LEFT JOIN users u ON ucr.user_id = u.id
			ORDER BY ucr.received_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	} else {
		// 用户查询自己的记录
		query = `
			SELECT ucr.id, ucr.provider, ucr.callback_type, ucr.order_no, ucr.customer_order_no, ucr.tracking_no, ucr.user_id,
			       ucr.raw_data, ucr.raw_signature, ucr.standardized_data, ucr.event_type, ucr.internal_status, ucr.external_status,
			       ucr.received_at, ucr.internal_processed_at, ucr.external_processed_at, ucr.internal_error, ucr.external_error,
			       ucr.retry_count, ucr.created_at, ucr.updated_at,
			       COALESCE(u.username, '') as username
			FROM unified_callback_records ucr
			LEFT JOIN users u ON ucr.user_id = u.id
			WHERE ucr.user_id = $1
			ORDER BY ucr.received_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{userID, limit, offset}
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询回调记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.UnifiedCallbackRecord
	for rows.Next() {
		var record model.UnifiedCallbackRecord
		var username string
		err := rows.Scan(
			&record.ID,
			&record.Provider,
			&record.CallbackType,
			&record.OrderNo,
			&record.CustomerOrderNo,
			&record.TrackingNo,
			&record.UserID,
			&record.RawData,
			&record.RawSignature,
			&record.StandardizedData,
			&record.EventType,
			&record.InternalStatus,
			&record.ExternalStatus,
			&record.ReceivedAt,
			&record.InternalProcessedAt,
			&record.ExternalProcessedAt,
			&record.InternalError,
			&record.ExternalError,
			&record.RetryCount,
			&record.CreatedAt,
			&record.UpdatedAt,
			&username,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描回调记录失败: %w", err)
		}

		// 将用户名添加到记录中（通过扩展字段）
		record.Username = username
		records = append(records, &record)
	}

	return records, nil
}

// GetCallbackRecordsCount 获取回调记录总数（管理员可传空userID查询所有记录）
func (r *PostgresCallbackRepository) GetCallbackRecordsCount(ctx context.Context, userID string) (int64, error) {
	var query string
	var args []interface{}

	if userID == "" {
		// 管理员查询所有记录总数
		query = `SELECT COUNT(*) FROM unified_callback_records`
		args = []interface{}{}
	} else {
		// 用户查询自己的记录总数
		query = `SELECT COUNT(*) FROM unified_callback_records WHERE user_id = $1`
		args = []interface{}{userID}
	}

	var count int64
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询回调记录总数失败: %w", err)
	}

	return count, nil
}

// SaveForwardRecord 保存转发记录
func (r *PostgresCallbackRepository) SaveForwardRecord(ctx context.Context, record *model.CallbackForwardRecord) error {
	if record.ID == uuid.Nil {
		record.ID = uuid.New()
	}
	record.CreatedAt = util.NowBeijing()

	query := `
		INSERT INTO callback_forward_records (
			id, callback_record_id, user_id, callback_url, request_data, response_data,
			http_status, status, retry_count, error_message, request_at, response_at, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ID,
		record.CallbackRecordID,
		record.UserID,
		record.CallbackURL,
		record.RequestData,
		record.ResponseData,
		record.HTTPStatus,
		record.Status,
		record.RetryCount,
		record.ErrorMessage,
		record.RequestAt,
		record.ResponseAt,
		record.CreatedAt,
	)

	if err != nil {
		return fmt.Errorf("保存转发记录失败: %w", err)
	}

	return nil
}

// GetForwardRecord 获取转发记录
func (r *PostgresCallbackRepository) GetForwardRecord(ctx context.Context, id uuid.UUID) (*model.CallbackForwardRecord, error) {
	query := `
		SELECT id, callback_record_id, user_id, callback_url, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at
		FROM callback_forward_records
		WHERE id = $1
	`

	var record model.CallbackForwardRecord
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&record.ID,
		&record.CallbackRecordID,
		&record.UserID,
		&record.CallbackURL,
		&record.RequestData,
		&record.ResponseData,
		&record.HTTPStatus,
		&record.Status,
		&record.RetryCount,
		&record.ErrorMessage,
		&record.RequestAt,
		&record.ResponseAt,
		&record.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("转发记录不存在")
		}
		return nil, fmt.Errorf("获取转发记录失败: %w", err)
	}

	return &record, nil
}

// UpdateForwardRecord 更新转发记录
func (r *PostgresCallbackRepository) UpdateForwardRecord(ctx context.Context, record *model.CallbackForwardRecord) error {
	query := `
		UPDATE callback_forward_records SET
			response_data = $1,
			http_status = $2,
			status = $3,
			retry_count = $4,
			error_message = $5,
			response_at = $6
		WHERE id = $7
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ResponseData,
		record.HTTPStatus,
		record.Status,
		record.RetryCount,
		record.ErrorMessage,
		record.ResponseAt,
		record.ID,
	)

	if err != nil {
		return fmt.Errorf("更新转发记录失败: %w", err)
	}

	return nil
}

// GetPendingForwardRecords 获取待处理的转发记录
func (r *PostgresCallbackRepository) GetPendingForwardRecords(ctx context.Context, limit int) ([]*model.CallbackForwardRecord, error) {
	query := `
		SELECT id, callback_record_id, user_id, callback_url, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at
		FROM callback_forward_records
		WHERE status = 'pending' OR (status = 'failed' AND retry_count < 3)
		ORDER BY created_at ASC
		LIMIT $1
	`

	rows, err := r.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("查询待处理转发记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.CallbackForwardRecord
	for rows.Next() {
		var record model.CallbackForwardRecord
		err := rows.Scan(
			&record.ID,
			&record.CallbackRecordID,
			&record.UserID,
			&record.CallbackURL,
			&record.RequestData,
			&record.ResponseData,
			&record.HTTPStatus,
			&record.Status,
			&record.RetryCount,
			&record.ErrorMessage,
			&record.RequestAt,
			&record.ResponseAt,
			&record.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描转发记录失败: %w", err)
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetForwardRecordsByUserID 根据用户ID获取转发记录
func (r *PostgresCallbackRepository) GetForwardRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.CallbackForwardRecord, error) {
	query := `
		SELECT id, callback_record_id, user_id, callback_url, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at
		FROM callback_forward_records
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询用户转发记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.CallbackForwardRecord
	for rows.Next() {
		var record model.CallbackForwardRecord
		err := rows.Scan(
			&record.ID,
			&record.CallbackRecordID,
			&record.UserID,
			&record.CallbackURL,
			&record.RequestData,
			&record.ResponseData,
			&record.HTTPStatus,
			&record.Status,
			&record.RetryCount,
			&record.ErrorMessage,
			&record.RequestAt,
			&record.ResponseAt,
			&record.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描转发记录失败: %w", err)
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetForwardRecordsCountByUserID 获取用户转发记录总数
func (r *PostgresCallbackRepository) GetForwardRecordsCountByUserID(ctx context.Context, userID string) (int64, error) {
	query := `
		SELECT COUNT(*) FROM (
			SELECT id FROM callback_forward_records WHERE user_id = $1
			UNION ALL
			SELECT id FROM work_order_forward_records WHERE user_id = $1
		) AS t
	`

	var count int64
	err := r.db.QueryRowContext(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询用户转发记录总数失败: %w", err)
	}

	return count, nil
}

// GetForwardRecordsByCallbackID 根据回调记录ID获取转发记录
func (r *PostgresCallbackRepository) GetForwardRecordsByCallbackID(ctx context.Context, callbackRecordID uuid.UUID) ([]*model.CallbackForwardRecord, error) {
	query := `
		SELECT id, callback_record_id, user_id, callback_url, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at
		FROM callback_forward_records
		WHERE callback_record_id = $1
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, callbackRecordID)
	if err != nil {
		return nil, fmt.Errorf("查询回调转发记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.CallbackForwardRecord
	for rows.Next() {
		var record model.CallbackForwardRecord
		err := rows.Scan(
			&record.ID,
			&record.CallbackRecordID,
			&record.UserID,
			&record.CallbackURL,
			&record.RequestData,
			&record.ResponseData,
			&record.HTTPStatus,
			&record.Status,
			&record.RetryCount,
			&record.ErrorMessage,
			&record.RequestAt,
			&record.ResponseAt,
			&record.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描回调转发记录失败: %w", err)
		}
		records = append(records, &record)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历回调转发记录失败: %w", err)
	}

	return records, nil
}

// GetCallbackStatistics 获取回调统计信息
func (r *PostgresCallbackRepository) GetCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error) {
	query := `
		SELECT
			COUNT(*) as total_callbacks,
			COUNT(CASE WHEN internal_status = 'success' THEN 1 END) as internal_success,
			COUNT(CASE WHEN external_status = 'success' THEN 1 END) as external_success,
			COUNT(CASE WHEN internal_status = 'failed' THEN 1 END) as internal_failed,
			COUNT(CASE WHEN external_status = 'failed' THEN 1 END) as external_failed,
			COUNT(DISTINCT provider) as provider_count,
			COUNT(DISTINCT event_type) as event_type_count
		FROM unified_callback_records
		WHERE ($1 = '' OR user_id = $1)
		  AND received_at BETWEEN $2 AND $3
	`

	var stats struct {
		TotalCallbacks  int `json:"total_callbacks"`
		InternalSuccess int `json:"internal_success"`
		ExternalSuccess int `json:"external_success"`
		InternalFailed  int `json:"internal_failed"`
		ExternalFailed  int `json:"external_failed"`
		ProviderCount   int `json:"provider_count"`
		EventTypeCount  int `json:"event_type_count"`
	}

	err := r.db.QueryRowContext(ctx, query, userID, startTime, endTime).Scan(
		&stats.TotalCallbacks,
		&stats.InternalSuccess,
		&stats.ExternalSuccess,
		&stats.InternalFailed,
		&stats.ExternalFailed,
		&stats.ProviderCount,
		&stats.EventTypeCount,
	)

	if err != nil {
		return nil, fmt.Errorf("获取回调统计信息失败: %w", err)
	}

	result := map[string]interface{}{
		"total_callbacks":  stats.TotalCallbacks,
		"internal_success": stats.InternalSuccess,
		"external_success": stats.ExternalSuccess,
		"internal_failed":  stats.InternalFailed,
		"external_failed":  stats.ExternalFailed,
		"provider_count":   stats.ProviderCount,
		"event_type_count": stats.EventTypeCount,
	}

	return result, nil
}

// GetForwardRecordStatistics 获取转发记录统计信息（用户用）
func (r *PostgresCallbackRepository) GetForwardRecordStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error) {
	query := `
		SELECT
			COUNT(*) as total_forwards,
			COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
			COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
			COALESCE(AVG(CASE
				WHEN response_at IS NOT NULL AND request_at IS NOT NULL
				THEN EXTRACT(EPOCH FROM (response_at - request_at))
			END), 0) as avg_response_time
		FROM callback_forward_records
		WHERE user_id = $1
		  AND created_at BETWEEN $2 AND $3
	`

	var stats struct {
		TotalForwards   int     `json:"total_forwards"`
		SuccessCount    int     `json:"success_count"`
		FailedCount     int     `json:"failed_count"`
		PendingCount    int     `json:"pending_count"`
		AvgResponseTime float64 `json:"avg_response_time"`
	}

	err := r.db.QueryRowContext(ctx, query, userID, startTime, endTime).Scan(
		&stats.TotalForwards,
		&stats.SuccessCount,
		&stats.FailedCount,
		&stats.PendingCount,
		&stats.AvgResponseTime,
	)

	if err != nil {
		return nil, fmt.Errorf("获取转发记录统计信息失败: %w", err)
	}

	// 计算成功率
	successRate := 0.0
	if stats.TotalForwards > 0 {
		successRate = float64(stats.SuccessCount) / float64(stats.TotalForwards) * 100
	}

	result := map[string]interface{}{
		"total_records":     stats.TotalForwards,
		"success_count":     stats.SuccessCount,
		"failed_count":      stats.FailedCount,
		"pending_count":     stats.PendingCount,
		"success_rate":      successRate,
		"avg_response_time": stats.AvgResponseTime,
	}

	return result, nil
}

// GetAdminCallbackStatistics 获取管理员回调统计信息（全局统计）
func (r *PostgresCallbackRepository) GetAdminCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time, provider, eventType string) (map[string]interface{}, error) {
	// 基础统计查询
	baseQuery := `
		SELECT
			COUNT(*) as total_records,
			COUNT(CASE WHEN internal_status = 'success' THEN 1 END) as success_records,
			COUNT(CASE WHEN internal_status = 'failed' THEN 1 END) as failed_records,
			COUNT(CASE WHEN internal_status IN ('pending', 'processing') THEN 1 END) as pending_records,
			COALESCE(AVG(CASE
				WHEN internal_processed_at IS NOT NULL AND received_at IS NOT NULL
				THEN EXTRACT(EPOCH FROM (internal_processed_at - received_at))
			END), 0) as avg_processing_time
		FROM unified_callback_records
		WHERE received_at BETWEEN $1 AND $2
	`

	args := []interface{}{startTime, endTime}
	argIndex := 3

	// 添加用户ID过滤
	if userID != "" {
		baseQuery += fmt.Sprintf(" AND user_id = $%d", argIndex)
		args = append(args, userID)
		argIndex++
	}

	// 添加供应商过滤
	if provider != "" {
		baseQuery += fmt.Sprintf(" AND provider = $%d", argIndex)
		args = append(args, provider)
		argIndex++
	}

	// 添加事件类型过滤
	if eventType != "" {
		baseQuery += fmt.Sprintf(" AND event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	var baseStats struct {
		TotalRecords      int     `json:"total_records"`
		SuccessRecords    int     `json:"success_records"`
		FailedRecords     int     `json:"failed_records"`
		PendingRecords    int     `json:"pending_records"`
		AvgProcessingTime float64 `json:"avg_processing_time"`
	}

	err := r.db.QueryRowContext(ctx, baseQuery, args...).Scan(
		&baseStats.TotalRecords,
		&baseStats.SuccessRecords,
		&baseStats.FailedRecords,
		&baseStats.PendingRecords,
		&baseStats.AvgProcessingTime,
	)

	if err != nil {
		return nil, fmt.Errorf("获取基础统计信息失败: %w", err)
	}

	// 计算成功率
	successRate := 0.0
	if baseStats.TotalRecords > 0 {
		successRate = (float64(baseStats.SuccessRecords) / float64(baseStats.TotalRecords)) * 100
	}

	// 供应商统计
	providerStats, err := r.getProviderStatistics(ctx, userID, startTime, endTime, provider, eventType)
	if err != nil {
		return nil, fmt.Errorf("获取供应商统计失败: %w", err)
	}

	// 事件类型统计
	eventTypeStats, err := r.getEventTypeStatistics(ctx, userID, startTime, endTime, provider, eventType)
	if err != nil {
		return nil, fmt.Errorf("获取事件类型统计失败: %w", err)
	}

	// 每日统计
	dailyStats, err := r.getDailyStatistics(ctx, userID, startTime, endTime, provider, eventType)
	if err != nil {
		return nil, fmt.Errorf("获取每日统计失败: %w", err)
	}

	result := map[string]interface{}{
		"total_records":       baseStats.TotalRecords,
		"success_records":     baseStats.SuccessRecords,
		"failed_records":      baseStats.FailedRecords,
		"pending_records":     baseStats.PendingRecords,
		"success_rate":        successRate,
		"avg_processing_time": baseStats.AvgProcessingTime,
		"provider_stats":      providerStats,
		"event_type_stats":    eventTypeStats,
		"daily_stats":         dailyStats,
	}

	return result, nil
}

// getProviderStatistics 获取供应商统计
func (r *PostgresCallbackRepository) getProviderStatistics(ctx context.Context, userID string, startTime, endTime time.Time, provider, eventType string) (map[string]interface{}, error) {
	query := `
		SELECT
			provider,
			COUNT(*) as total,
			COUNT(CASE WHEN internal_status = 'success' THEN 1 END) as success,
			COUNT(CASE WHEN internal_status = 'failed' THEN 1 END) as failed,
			COUNT(CASE WHEN internal_status IN ('pending', 'processing') THEN 1 END) as pending
		FROM unified_callback_records
		WHERE received_at BETWEEN $1 AND $2
	`

	args := []interface{}{startTime, endTime}
	argIndex := 3

	// 添加过滤条件
	if userID != "" {
		query += fmt.Sprintf(" AND user_id = $%d", argIndex)
		args = append(args, userID)
		argIndex++
	}

	if provider != "" {
		query += fmt.Sprintf(" AND provider = $%d", argIndex)
		args = append(args, provider)
		argIndex++
	}

	if eventType != "" {
		query += fmt.Sprintf(" AND event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	query += " GROUP BY provider ORDER BY total DESC"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询供应商统计失败: %w", err)
	}
	defer rows.Close()

	result := make(map[string]interface{})
	for rows.Next() {
		var providerName string
		var total, success, failed, pending int

		err := rows.Scan(&providerName, &total, &success, &failed, &pending)
		if err != nil {
			return nil, fmt.Errorf("扫描供应商统计数据失败: %w", err)
		}

		result[providerName] = map[string]int{
			"total":   total,
			"success": success,
			"failed":  failed,
			"pending": pending,
		}
	}

	return result, nil
}

// getEventTypeStatistics 获取事件类型统计
func (r *PostgresCallbackRepository) getEventTypeStatistics(ctx context.Context, userID string, startTime, endTime time.Time, provider, eventType string) (map[string]interface{}, error) {
	query := `
		SELECT
			event_type,
			COUNT(*) as total,
			COUNT(CASE WHEN internal_status = 'success' THEN 1 END) as success,
			COUNT(CASE WHEN internal_status = 'failed' THEN 1 END) as failed,
			COUNT(CASE WHEN internal_status IN ('pending', 'processing') THEN 1 END) as pending
		FROM unified_callback_records
		WHERE received_at BETWEEN $1 AND $2
	`

	args := []interface{}{startTime, endTime}
	argIndex := 3

	// 添加过滤条件
	if userID != "" {
		query += fmt.Sprintf(" AND user_id = $%d", argIndex)
		args = append(args, userID)
		argIndex++
	}

	if provider != "" {
		query += fmt.Sprintf(" AND provider = $%d", argIndex)
		args = append(args, provider)
		argIndex++
	}

	if eventType != "" {
		query += fmt.Sprintf(" AND event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	query += " GROUP BY event_type ORDER BY total DESC"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询事件类型统计失败: %w", err)
	}
	defer rows.Close()

	result := make(map[string]interface{})
	for rows.Next() {
		var eventTypeName string
		var total, success, failed, pending int

		err := rows.Scan(&eventTypeName, &total, &success, &failed, &pending)
		if err != nil {
			return nil, fmt.Errorf("扫描事件类型统计数据失败: %w", err)
		}

		result[eventTypeName] = map[string]int{
			"total":   total,
			"success": success,
			"failed":  failed,
			"pending": pending,
		}
	}

	return result, nil
}

// getDailyStatistics 获取每日统计
func (r *PostgresCallbackRepository) getDailyStatistics(ctx context.Context, userID string, startTime, endTime time.Time, provider, eventType string) ([]map[string]interface{}, error) {
	query := `
		SELECT
			DATE(received_at) as date,
			COUNT(*) as total,
			COUNT(CASE WHEN internal_status = 'success' THEN 1 END) as success,
			COUNT(CASE WHEN internal_status = 'failed' THEN 1 END) as failed,
			COUNT(CASE WHEN internal_status IN ('pending', 'processing') THEN 1 END) as pending
		FROM unified_callback_records
		WHERE received_at BETWEEN $1 AND $2
	`

	args := []interface{}{startTime, endTime}
	argIndex := 3

	// 添加过滤条件
	if userID != "" {
		query += fmt.Sprintf(" AND user_id = $%d", argIndex)
		args = append(args, userID)
		argIndex++
	}

	if provider != "" {
		query += fmt.Sprintf(" AND provider = $%d", argIndex)
		args = append(args, provider)
		argIndex++
	}

	if eventType != "" {
		query += fmt.Sprintf(" AND event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	query += " GROUP BY DATE(received_at) ORDER BY date DESC LIMIT 30"

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询每日统计失败: %w", err)
	}
	defer rows.Close()

	var result []map[string]interface{}
	for rows.Next() {
		var date time.Time
		var total, success, failed, pending int

		err := rows.Scan(&date, &total, &success, &failed, &pending)
		if err != nil {
			return nil, fmt.Errorf("扫描每日统计数据失败: %w", err)
		}

		result = append(result, map[string]interface{}{
			"date":    date.Format("2006-01-02"),
			"total":   total,
			"success": success,
			"failed":  failed,
			"pending": pending,
		})
	}

	return result, nil
}

// 🔥 优化版：高性能增强转发记录查询（优化UNION ALL和分页）
func (r *PostgresCallbackRepository) GetEnhancedForwardRecordsWithJoinOptimized(ctx context.Context, userID string, limit, offset int, filters map[string]string) ([]*model.EnhancedForwardRecord, error) {
	// 🔥 性能优化策略：
	// 1. 分别查询两个表，利用各自的索引
	// 2. 在应用层合并和排序，避免数据库层面的UNION ALL性能问题
	// 3. 针对运单号筛选进行精确匹配优化

	var allRecords []*model.EnhancedForwardRecord

	// 检查是否有运单号筛选
	trackingNo := filters["tracking_no"]
	isTrackingNoFilter := trackingNo != ""

	// 🔥 优化1：如果有运单号筛选，优先使用精确匹配
	if isTrackingNoFilter {
		// 尝试精确匹配
		exactRecords, err := r.getRecordsByExactTrackingNo(ctx, userID, trackingNo, filters)
		if err != nil {
			return nil, fmt.Errorf("精确匹配运单号查询失败: %w", err)
		}

		// 如果精确匹配有结果，直接返回
		if len(exactRecords) > 0 {
			// 应用分页
			start := offset
			end := offset + limit
			if start >= len(exactRecords) {
				return []*model.EnhancedForwardRecord{}, nil
			}
			if end > len(exactRecords) {
				end = len(exactRecords)
			}
			return exactRecords[start:end], nil
		}

		// 如果精确匹配无结果，继续模糊查询
	}

	// 🔥 优化2：分别查询两个表，利用各自的索引
	// 查询callback_forward_records
	callbackRecords, err := r.getCallbackForwardRecordsOptimized(ctx, userID, limit*2, 0, filters)
	if err != nil {
		return nil, fmt.Errorf("查询回调转发记录失败: %w", err)
	}
	allRecords = append(allRecords, callbackRecords...)

	// 查询work_order_forward_records
	workOrderRecords, err := r.getWorkOrderForwardRecordsOptimized(ctx, userID, limit*2, 0, filters)
	if err != nil {
		return nil, fmt.Errorf("查询工单转发记录失败: %w", err)
	}
	allRecords = append(allRecords, workOrderRecords...)

	// 🔥 优化3：在应用层排序和分页
	sort.Slice(allRecords, func(i, j int) bool {
		return allRecords[i].CreatedAt.After(allRecords[j].CreatedAt)
	})

	// 应用分页
	start := offset
	end := offset + limit
	if start >= len(allRecords) {
		return []*model.EnhancedForwardRecord{}, nil
	}
	if end > len(allRecords) {
		end = len(allRecords)
	}

	return allRecords[start:end], nil
}

// 🔥 新增：精确匹配运单号查询
func (r *PostgresCallbackRepository) getRecordsByExactTrackingNo(ctx context.Context, userID, trackingNo string, filters map[string]string) ([]*model.EnhancedForwardRecord, error) {
	var records []*model.EnhancedForwardRecord

	// 查询callback_forward_records中的精确匹配
	query1 := `
		SELECT
			cfr.id, cfr.callback_url, cfr.request_data, cfr.response_data,
			cfr.http_status, cfr.status, cfr.retry_count, cfr.error_message,
			cfr.request_at, cfr.response_at, cfr.created_at,
			COALESCE(ucr.event_type, '') AS event_type,
			COALESCE(ucr.order_no, '') AS order_no,
			COALESCE(ucr.customer_order_no, '') AS customer_order_no,
			COALESCE(ucr.tracking_no, '') AS tracking_no
		FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1 AND ucr.tracking_no = $2`

	// 添加其他筛选条件
	args1 := []interface{}{userID, trackingNo}
	argIndex := 3

	if eventType := filters["event_type"]; eventType != "" {
		query1 += fmt.Sprintf(" AND ucr.event_type = $%d", argIndex)
		args1 = append(args1, eventType)
		argIndex++
	}

	if status := filters["status"]; status != "" {
		query1 += fmt.Sprintf(" AND cfr.status = $%d", argIndex)
		args1 = append(args1, status)
		argIndex++
	}

	query1 += " ORDER BY cfr.created_at DESC"

	rows1, err := r.db.QueryContext(ctx, query1, args1...)
	if err != nil {
		return nil, err
	}
	defer rows1.Close()

	for rows1.Next() {
		var record model.EnhancedForwardRecord
		err := rows1.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, err
		}
		records = append(records, &record)
	}

	// 查询work_order_forward_records中的精确匹配
	query2 := `
		SELECT
			wfr.id, wfr.callback_url, wfr.request_data, wfr.response_data,
			wfr.http_status, wfr.status, wfr.retry_count, wfr.error_message,
			wfr.request_at, wfr.response_at, wfr.created_at,
			wfr.event_type AS event_type,
			COALESCE(wo.order_no, '') AS order_no,
			COALESCE(wo.order_no, '') AS customer_order_no,
			COALESCE(wo.tracking_no, '') AS tracking_no
		FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1 AND wo.tracking_no = $2`

	args2 := []interface{}{userID, trackingNo}
	argIndex = 3

	if eventType := filters["event_type"]; eventType != "" {
		query2 += fmt.Sprintf(" AND wfr.event_type = $%d", argIndex)
		args2 = append(args2, eventType)
		argIndex++
	}

	if status := filters["status"]; status != "" {
		query2 += fmt.Sprintf(" AND wfr.status = $%d", argIndex)
		args2 = append(args2, status)
		argIndex++
	}

	query2 += " ORDER BY wfr.created_at DESC"

	rows2, err := r.db.QueryContext(ctx, query2, args2...)
	if err != nil {
		return nil, err
	}
	defer rows2.Close()

	for rows2.Next() {
		var record model.EnhancedForwardRecord
		err := rows2.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, err
		}
		records = append(records, &record)
	}

	// 按创建时间排序
	sort.Slice(records, func(i, j int) bool {
		return records[i].CreatedAt.After(records[j].CreatedAt)
	})

	return records, nil
}

// 🔥 新增：优化的回调转发记录查询
func (r *PostgresCallbackRepository) getCallbackForwardRecordsOptimized(ctx context.Context, userID string, limit, offset int, filters map[string]string) ([]*model.EnhancedForwardRecord, error) {
	query := `
		SELECT
			cfr.id, cfr.callback_url, cfr.request_data, cfr.response_data,
			cfr.http_status, cfr.status, cfr.retry_count, cfr.error_message,
			cfr.request_at, cfr.response_at, cfr.created_at,
			COALESCE(ucr.event_type, '') AS event_type,
			COALESCE(ucr.order_no, '') AS order_no,
			COALESCE(ucr.customer_order_no, '') AS customer_order_no,
			COALESCE(ucr.tracking_no, '') AS tracking_no
		FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1`

	args := []interface{}{userID}
	argIndex := 2

	// 运单号筛选 - 优化：如果运单号不为空，使用ILIKE模糊查询
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		query += fmt.Sprintf(" AND ucr.tracking_no ILIKE $%d", argIndex)
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		query += fmt.Sprintf(" AND ucr.event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		query += fmt.Sprintf(" AND cfr.status = $%d", argIndex)
		args = append(args, status)
		argIndex++
	}

	query += fmt.Sprintf(" ORDER BY cfr.created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var records []*model.EnhancedForwardRecord
	for rows.Next() {
		var record model.EnhancedForwardRecord
		err := rows.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, err
		}
		records = append(records, &record)
	}

	return records, nil
}

// 🔥 新增：优化的工单转发记录查询
func (r *PostgresCallbackRepository) getWorkOrderForwardRecordsOptimized(ctx context.Context, userID string, limit, offset int, filters map[string]string) ([]*model.EnhancedForwardRecord, error) {
	query := `
		SELECT
			wfr.id, wfr.callback_url, wfr.request_data, wfr.response_data,
			wfr.http_status, wfr.status, wfr.retry_count, wfr.error_message,
			wfr.request_at, wfr.response_at, wfr.created_at,
			wfr.event_type AS event_type,
			COALESCE(wo.order_no, '') AS order_no,
			COALESCE(wo.order_no, '') AS customer_order_no,
			COALESCE(wo.tracking_no, '') AS tracking_no
		FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1`

	args := []interface{}{userID}
	argIndex := 2

	// 运单号筛选
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		query += fmt.Sprintf(" AND wo.tracking_no ILIKE $%d", argIndex)
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		query += fmt.Sprintf(" AND wfr.event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		query += fmt.Sprintf(" AND wfr.status = $%d", argIndex)
		args = append(args, status)
		argIndex++
	}

	query += fmt.Sprintf(" ORDER BY wfr.created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var records []*model.EnhancedForwardRecord
	for rows.Next() {
		var record model.EnhancedForwardRecord
		err := rows.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, err
		}
		records = append(records, &record)
	}

	return records, nil
}

// 🔥 新增：高性能增强转发记录查询（使用JOIN避免N+1问题）
func (r *PostgresCallbackRepository) GetEnhancedForwardRecordsWithJoin(ctx context.Context, userID string, limit, offset int, filters map[string]string) ([]*model.EnhancedForwardRecord, error) {
	// 构建基础查询
	baseQuery := `
		SELECT
			cfr.id, cfr.callback_url, cfr.request_data, cfr.response_data,
			cfr.http_status, cfr.status, cfr.retry_count, cfr.error_message,
			cfr.request_at, cfr.response_at, cfr.created_at,
			COALESCE(ucr.event_type, '')                 AS event_type,
			COALESCE(ucr.order_no, '')                  AS order_no,
			COALESCE(ucr.customer_order_no, '')         AS customer_order_no,
			COALESCE(ucr.tracking_no, '')               AS tracking_no
		FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1`

	workOrderQuery := `
		SELECT
			wfr.id, wfr.callback_url, wfr.request_data, wfr.response_data,
			wfr.http_status, wfr.status, wfr.retry_count, wfr.error_message,
			wfr.request_at, wfr.response_at, wfr.created_at,
			wfr.event_type                                        AS event_type,
			COALESCE(wo.order_no, '')                             AS order_no,
			COALESCE(wo.order_no, '')                             AS customer_order_no,
			COALESCE(wo.tracking_no, '')                          AS tracking_no
		FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1`

	// 构建筛选条件
	var conditions []string
	var args []interface{}
	argIndex := 2 // 第一个参数是userID

	// 运单号筛选
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		conditions = append(conditions, fmt.Sprintf("COALESCE(ucr.tracking_no, '') ILIKE $%d", argIndex))
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		conditions = append(conditions, fmt.Sprintf("COALESCE(ucr.event_type, '') = $%d", argIndex))
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		conditions = append(conditions, fmt.Sprintf("cfr.status = $%d", argIndex))
		args = append(args, status)
		argIndex++
	}

	// 应用筛选条件到基础查询
	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// 为工单查询构建相应的筛选条件
	var workOrderConditions []string
	workOrderArgIndex := argIndex

	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		workOrderConditions = append(workOrderConditions, fmt.Sprintf("COALESCE(wo.tracking_no, '') ILIKE $%d", workOrderArgIndex))
		workOrderArgIndex++
	}

	if eventType := filters["event_type"]; eventType != "" {
		workOrderConditions = append(workOrderConditions, fmt.Sprintf("wfr.event_type = $%d", workOrderArgIndex))
		workOrderArgIndex++
	}

	if status := filters["status"]; status != "" {
		workOrderConditions = append(workOrderConditions, fmt.Sprintf("wfr.status = $%d", workOrderArgIndex))
		workOrderArgIndex++
	}

	if len(workOrderConditions) > 0 {
		workOrderQuery += " AND " + strings.Join(workOrderConditions, " AND ")
	}

	// 组合完整查询
	query := fmt.Sprintf(`
		%s
		UNION ALL
		%s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d
	`, baseQuery, workOrderQuery, workOrderArgIndex, workOrderArgIndex+1)

	// 准备参数：userID + 筛选参数 + 筛选参数(工单) + limit + offset
	queryArgs := []interface{}{userID}
	queryArgs = append(queryArgs, args...)
	queryArgs = append(queryArgs, args...) // 工单查询使用相同的筛选参数
	queryArgs = append(queryArgs, limit, offset)

	rows, err := r.db.QueryContext(ctx, query, queryArgs...)
	if err != nil {
		return nil, fmt.Errorf("查询增强转发记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.EnhancedForwardRecord
	for rows.Next() {
		var record model.EnhancedForwardRecord
		err := rows.Scan(
			&record.ID,
			&record.CallbackURL,
			&record.RequestData,
			&record.ResponseData,
			&record.HTTPStatus,
			&record.Status,
			&record.RetryCount,
			&record.ErrorMessage,
			&record.RequestAt,
			&record.ResponseAt,
			&record.CreatedAt,
			&record.EventType,
			&record.OrderNo,
			&record.CustomerOrderNo,
			&record.TrackingNo,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描增强转发记录失败: %w", err)
		}
		records = append(records, &record)
	}

	return records, nil
}

// 🔥 优化版：获取增强转发记录总数（支持筛选，优化性能）
func (r *PostgresCallbackRepository) GetEnhancedForwardRecordsCountWithFiltersOptimized(ctx context.Context, userID string, filters map[string]string) (int64, error) {
	// 🔥 性能优化：分别查询两个表的计数，避免UNION ALL

	var totalCount int64

	// 检查是否有运单号筛选
	trackingNo := filters["tracking_no"]
	isTrackingNoFilter := trackingNo != ""

	// 🔥 优化1：如果有运单号筛选，优先使用精确匹配计数
	if isTrackingNoFilter {
		exactCount, err := r.getCountByExactTrackingNo(ctx, userID, trackingNo, filters)
		if err != nil {
			return 0, fmt.Errorf("精确匹配运单号计数失败: %w", err)
		}

		// 如果精确匹配有结果，直接返回
		if exactCount > 0 {
			return exactCount, nil
		}

		// 如果精确匹配无结果，继续模糊查询计数
	}

	// 🔥 优化2：分别查询两个表的计数
	// 查询callback_forward_records计数
	callbackCount, err := r.getCallbackForwardRecordsCount(ctx, userID, filters)
	if err != nil {
		return 0, fmt.Errorf("查询回调转发记录计数失败: %w", err)
	}
	totalCount += callbackCount

	// 查询work_order_forward_records计数
	workOrderCount, err := r.getWorkOrderForwardRecordsCount(ctx, userID, filters)
	if err != nil {
		return 0, fmt.Errorf("查询工单转发记录计数失败: %w", err)
	}
	totalCount += workOrderCount

	return totalCount, nil
}

// 🔥 新增：精确匹配运单号计数查询
func (r *PostgresCallbackRepository) getCountByExactTrackingNo(ctx context.Context, userID, trackingNo string, filters map[string]string) (int64, error) {
	var totalCount int64

	// 查询callback_forward_records中的精确匹配计数
	query1 := `
		SELECT COUNT(*)
		FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1 AND ucr.tracking_no = $2`

	args1 := []interface{}{userID, trackingNo}
	argIndex := 3

	if eventType := filters["event_type"]; eventType != "" {
		query1 += fmt.Sprintf(" AND ucr.event_type = $%d", argIndex)
		args1 = append(args1, eventType)
		argIndex++
	}

	if status := filters["status"]; status != "" {
		query1 += fmt.Sprintf(" AND cfr.status = $%d", argIndex)
		args1 = append(args1, status)
		argIndex++
	}

	var count1 int64
	err := r.db.QueryRowContext(ctx, query1, args1...).Scan(&count1)
	if err != nil {
		return 0, err
	}
	totalCount += count1

	// 查询work_order_forward_records中的精确匹配计数
	query2 := `
		SELECT COUNT(*)
		FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1 AND wo.tracking_no = $2`

	args2 := []interface{}{userID, trackingNo}
	argIndex = 3

	if eventType := filters["event_type"]; eventType != "" {
		query2 += fmt.Sprintf(" AND wfr.event_type = $%d", argIndex)
		args2 = append(args2, eventType)
		argIndex++
	}

	if status := filters["status"]; status != "" {
		query2 += fmt.Sprintf(" AND wfr.status = $%d", argIndex)
		args2 = append(args2, status)
		argIndex++
	}

	var count2 int64
	err = r.db.QueryRowContext(ctx, query2, args2...).Scan(&count2)
	if err != nil {
		return 0, err
	}
	totalCount += count2

	return totalCount, nil
}

// 🔥 新增：优化的回调转发记录计数查询
func (r *PostgresCallbackRepository) getCallbackForwardRecordsCount(ctx context.Context, userID string, filters map[string]string) (int64, error) {
	query := `
		SELECT COUNT(*)
		FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1`

	args := []interface{}{userID}
	argIndex := 2

	// 运单号筛选
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		query += fmt.Sprintf(" AND ucr.tracking_no ILIKE $%d", argIndex)
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		query += fmt.Sprintf(" AND ucr.event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		query += fmt.Sprintf(" AND cfr.status = $%d", argIndex)
		args = append(args, status)
		argIndex++
	}

	var count int64
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// 🔥 新增：优化的工单转发记录计数查询
func (r *PostgresCallbackRepository) getWorkOrderForwardRecordsCount(ctx context.Context, userID string, filters map[string]string) (int64, error) {
	query := `
		SELECT COUNT(*)
		FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1`

	args := []interface{}{userID}
	argIndex := 2

	// 运单号筛选
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		query += fmt.Sprintf(" AND wo.tracking_no ILIKE $%d", argIndex)
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		query += fmt.Sprintf(" AND wfr.event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		query += fmt.Sprintf(" AND wfr.status = $%d", argIndex)
		args = append(args, status)
		argIndex++
	}

	var count int64
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// 🔥 新增：获取增强转发记录总数（支持筛选）
func (r *PostgresCallbackRepository) GetEnhancedForwardRecordsCountWithFilters(ctx context.Context, userID string, filters map[string]string) (int64, error) {
	// 构建基础计数查询
	baseCountQuery := `
		SELECT COUNT(*) FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1`

	workOrderCountQuery := `
		SELECT COUNT(*) FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1`

	// 构建筛选条件
	var conditions []string
	var args []interface{}
	argIndex := 2 // 第一个参数是userID

	// 运单号筛选
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		conditions = append(conditions, fmt.Sprintf("COALESCE(ucr.tracking_no, '') ILIKE $%d", argIndex))
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		conditions = append(conditions, fmt.Sprintf("COALESCE(ucr.event_type, '') = $%d", argIndex))
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		conditions = append(conditions, fmt.Sprintf("cfr.status = $%d", argIndex))
		args = append(args, status)
		argIndex++
	}

	// 应用筛选条件到基础查询
	if len(conditions) > 0 {
		baseCountQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// 为工单查询构建相应的筛选条件
	var workOrderConditions []string
	workOrderArgIndex := argIndex

	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		workOrderConditions = append(workOrderConditions, fmt.Sprintf("COALESCE(wo.tracking_no, '') ILIKE $%d", workOrderArgIndex))
		workOrderArgIndex++
	}

	if eventType := filters["event_type"]; eventType != "" {
		workOrderConditions = append(workOrderConditions, fmt.Sprintf("wfr.event_type = $%d", workOrderArgIndex))
		workOrderArgIndex++
	}

	if status := filters["status"]; status != "" {
		workOrderConditions = append(workOrderConditions, fmt.Sprintf("wfr.status = $%d", workOrderArgIndex))
		workOrderArgIndex++
	}

	if len(workOrderConditions) > 0 {
		workOrderCountQuery += " AND " + strings.Join(workOrderConditions, " AND ")
	}

	// 组合完整计数查询
	query := fmt.Sprintf(`
		SELECT ((%s) + (%s)) AS total_count
	`, baseCountQuery, workOrderCountQuery)

	// 准备参数：userID + 筛选参数 + 筛选参数(工单)
	queryArgs := []interface{}{userID}
	queryArgs = append(queryArgs, args...)
	queryArgs = append(queryArgs, args...) // 工单查询使用相同的筛选参数

	var count int64
	err := r.db.QueryRowContext(ctx, query, queryArgs...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询增强转发记录总数失败: %w", err)
	}

	return count, nil
}

// SaveWorkOrderForwardRecord 保存工单回调转发记录
func (r *PostgresCallbackRepository) SaveWorkOrderForwardRecord(ctx context.Context, record *model.WorkOrderForwardRecord) error {
	if record.ID == uuid.Nil {
		record.ID = uuid.New()
	}
	record.CreatedAt = util.NowBeijing()
	record.UpdatedAt = util.NowBeijing()

	// 🔥 修复：正确处理json.RawMessage类型的数据
	var requestData interface{}
	var responseData interface{}

	// 处理请求数据
	if len(record.RequestData) > 0 {
		// 验证JSON格式
		if json.Valid(record.RequestData) {
			requestData = record.RequestData
		} else {
			// 如果不是有效的JSON，转换为字符串
			requestData = string(record.RequestData)
		}
	} else {
		requestData = nil
	}

	// 处理响应数据
	if len(record.ResponseData) > 0 {
		// 验证JSON格式
		if json.Valid(record.ResponseData) {
			responseData = record.ResponseData
		} else {
			// 如果不是有效的JSON，转换为字符串
			responseData = string(record.ResponseData)
		}
	} else {
		responseData = nil
	}

	query := `
		INSERT INTO work_order_forward_records (
			id, work_order_id, user_id, callback_url, event_type, request_data, response_data,
			http_status, status, retry_count, error_message, request_at, response_at, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ID,
		record.WorkOrderID,
		record.UserID,
		record.CallbackURL,
		record.EventType,
		requestData,  // 🔥 使用处理后的数据
		responseData, // 🔥 使用处理后的数据
		record.HTTPStatus,
		record.Status,
		record.RetryCount,
		record.ErrorMessage,
		record.RequestAt,
		record.ResponseAt,
		record.CreatedAt,
		record.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("保存工单回调转发记录失败: %w", err)
	}

	return nil
}

// UpdateWorkOrderForwardRecord 更新工单回调转发记录
func (r *PostgresCallbackRepository) UpdateWorkOrderForwardRecord(ctx context.Context, record *model.WorkOrderForwardRecord) error {
	record.UpdatedAt = util.NowBeijing()

	// 🔥 修复：正确处理json.RawMessage类型的响应数据
	var responseData interface{}
	if len(record.ResponseData) > 0 {
		// 验证JSON格式
		if json.Valid(record.ResponseData) {
			responseData = record.ResponseData
		} else {
			// 如果不是有效的JSON，转换为字符串
			responseData = string(record.ResponseData)
		}
	} else {
		responseData = nil
	}

	query := `
		UPDATE work_order_forward_records SET
			response_data = $1,
			http_status = $2,
			status = $3,
			retry_count = $4,
			error_message = $5,
			response_at = $6,
			updated_at = $7
		WHERE id = $8
	`

	_, err := r.db.ExecContext(ctx, query,
		responseData, // 🔥 使用处理后的数据
		record.HTTPStatus,
		record.Status,
		record.RetryCount,
		record.ErrorMessage,
		record.ResponseAt,
		record.UpdatedAt,
		record.ID,
	)

	if err != nil {
		return fmt.Errorf("更新工单回调转发记录失败: %w", err)
	}

	return nil
}

// GetWorkOrderForwardRecord 获取工单回调转发记录
func (r *PostgresCallbackRepository) GetWorkOrderForwardRecord(ctx context.Context, id uuid.UUID) (*model.WorkOrderForwardRecord, error) {
	query := `
		SELECT id, work_order_id, user_id, callback_url, event_type, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at, updated_at
		FROM work_order_forward_records
		WHERE id = $1
	`

	var record model.WorkOrderForwardRecord
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&record.ID,
		&record.WorkOrderID,
		&record.UserID,
		&record.CallbackURL,
		&record.EventType,
		&record.RequestData,
		&record.ResponseData,
		&record.HTTPStatus,
		&record.Status,
		&record.RetryCount,
		&record.ErrorMessage,
		&record.RequestAt,
		&record.ResponseAt,
		&record.CreatedAt,
		&record.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("工单回调转发记录不存在")
		}
		return nil, fmt.Errorf("获取工单回调转发记录失败: %w", err)
	}

	return &record, nil
}

// GetFailedWorkOrderForwardRecords 获取失败次数超过maxRetries的工单回调转发记录
func (r *PostgresCallbackRepository) GetFailedWorkOrderForwardRecords(ctx context.Context, maxRetries int) ([]*model.WorkOrderForwardRecord, error) {
	query := `
		SELECT id, work_order_id, user_id, callback_url, event_type, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at, updated_at
		FROM work_order_forward_records
		WHERE status = 'failed' AND retry_count >= $1
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, maxRetries)
	if err != nil {
		return nil, fmt.Errorf("查询失败次数超过maxRetries的工单回调转发记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.WorkOrderForwardRecord
	for rows.Next() {
		var record model.WorkOrderForwardRecord
		err := rows.Scan(
			&record.ID,
			&record.WorkOrderID,
			&record.UserID,
			&record.CallbackURL,
			&record.EventType,
			&record.RequestData,
			&record.ResponseData,
			&record.HTTPStatus,
			&record.Status,
			&record.RetryCount,
			&record.ErrorMessage,
			&record.RequestAt,
			&record.ResponseAt,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描失败次数超过maxRetries的工单回调转发记录失败: %w", err)
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetWorkOrderForwardRecordsByUserID 根据用户ID获取工单回调转发记录
func (r *PostgresCallbackRepository) GetWorkOrderForwardRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.WorkOrderForwardRecord, error) {
	query := `
		SELECT id, work_order_id, user_id, callback_url, event_type, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at, updated_at
		FROM work_order_forward_records
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询用户工单回调转发记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.WorkOrderForwardRecord
	for rows.Next() {
		var record model.WorkOrderForwardRecord
		err := rows.Scan(
			&record.ID,
			&record.WorkOrderID,
			&record.UserID,
			&record.CallbackURL,
			&record.EventType,
			&record.RequestData,
			&record.ResponseData,
			&record.HTTPStatus,
			&record.Status,
			&record.RetryCount,
			&record.ErrorMessage,
			&record.RequestAt,
			&record.ResponseAt,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描用户工单回调转发记录失败: %w", err)
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetWorkOrderForwardRecordsCount 获取用户工单回调转发记录总数
func (r *PostgresCallbackRepository) GetWorkOrderForwardRecordsCount(ctx context.Context, userID string) (int64, error) {
	query := `SELECT COUNT(*) FROM work_order_forward_records WHERE user_id = $1`

	var count int64
	err := r.db.QueryRowContext(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询用户工单回调转发记录总数失败: %w", err)
	}

	return count, nil
}

// GetWorkOrderForwardRecordsByWorkOrderID 根据工单ID获取工单回调转发记录
func (r *PostgresCallbackRepository) GetWorkOrderForwardRecordsByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]*model.WorkOrderForwardRecord, error) {
	query := `
		SELECT id, work_order_id, user_id, callback_url, event_type, request_data, response_data,
		       http_status, status, retry_count, error_message, request_at, response_at, created_at, updated_at
		FROM work_order_forward_records
		WHERE work_order_id = $1
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, workOrderID)
	if err != nil {
		return nil, fmt.Errorf("查询工单回调转发记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.WorkOrderForwardRecord
	for rows.Next() {
		var record model.WorkOrderForwardRecord
		err := rows.Scan(
			&record.ID,
			&record.WorkOrderID,
			&record.UserID,
			&record.CallbackURL,
			&record.EventType,
			&record.RequestData,
			&record.ResponseData,
			&record.HTTPStatus,
			&record.Status,
			&record.RetryCount,
			&record.ErrorMessage,
			&record.RequestAt,
			&record.ResponseAt,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描工单回调转发记录失败: %w", err)
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetWorkOrderCallbackStatistics 获取工单回调统计信息
func (r *PostgresCallbackRepository) GetWorkOrderCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error) {
	query := `
		SELECT
			COUNT(*) as total_callbacks,
			COUNT(CASE WHEN internal_status = 'success' THEN 1 END) as internal_success,
			COUNT(CASE WHEN external_status = 'success' THEN 1 END) as external_success,
			COUNT(CASE WHEN internal_status = 'failed' THEN 1 END) as internal_failed,
			COUNT(CASE WHEN external_status = 'failed' THEN 1 END) as external_failed,
			COUNT(DISTINCT provider) as provider_count,
			COUNT(DISTINCT event_type) as event_type_count
		FROM work_order_forward_records
		WHERE ($1 = '' OR user_id = $1)
		  AND created_at BETWEEN $2 AND $3
	`

	var stats struct {
		TotalCallbacks  int `json:"total_callbacks"`
		InternalSuccess int `json:"internal_success"`
		ExternalSuccess int `json:"external_success"`
		InternalFailed  int `json:"internal_failed"`
		ExternalFailed  int `json:"external_failed"`
		ProviderCount   int `json:"provider_count"`
		EventTypeCount  int `json:"event_type_count"`
	}

	err := r.db.QueryRowContext(ctx, query, userID, startTime, endTime).Scan(
		&stats.TotalCallbacks,
		&stats.InternalSuccess,
		&stats.ExternalSuccess,
		&stats.InternalFailed,
		&stats.ExternalFailed,
		&stats.ProviderCount,
		&stats.EventTypeCount,
	)

	if err != nil {
		return nil, fmt.Errorf("获取工单回调统计信息失败: %w", err)
	}

	result := map[string]interface{}{
		"total_callbacks":  stats.TotalCallbacks,
		"internal_success": stats.InternalSuccess,
		"external_success": stats.ExternalSuccess,
		"internal_failed":  stats.InternalFailed,
		"external_failed":  stats.ExternalFailed,
		"provider_count":   stats.ProviderCount,
		"event_type_count": stats.EventTypeCount,
	}

	return result, nil
}

// 🔥 重试记录管理方法实现

// SaveRetryRecord 保存重试记录
func (r *PostgresCallbackRepository) SaveRetryRecord(ctx context.Context, record *model.CallbackRetryRecord) error {
	query := `
		INSERT INTO callback_retry_records (
			id, forward_record_id, callback_record_id, user_id, retry_attempt,
			scheduled_at, executed_at, status, error_message, http_status,
			response_data, execution_duration_ms, next_retry_at, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ID,
		record.ForwardRecordID,
		record.CallbackRecordID,
		record.UserID,
		record.RetryAttempt,
		record.ScheduledAt,
		record.ExecutedAt,
		record.Status,
		record.ErrorMessage,
		record.HTTPStatus,
		record.ResponseData,
		record.ExecutionDurationMs,
		record.NextRetryAt,
		record.CreatedAt,
		record.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("保存重试记录失败: %w", err)
	}

	return nil
}

// GetRetryRecord 获取重试记录
func (r *PostgresCallbackRepository) GetRetryRecord(ctx context.Context, id uuid.UUID) (*model.CallbackRetryRecord, error) {
	query := `
		SELECT id, forward_record_id, callback_record_id, user_id, retry_attempt,
			   scheduled_at, executed_at, status, error_message, http_status,
			   response_data, execution_duration_ms, next_retry_at, created_at, updated_at
		FROM callback_retry_records
		WHERE id = $1
	`

	record := &model.CallbackRetryRecord{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&record.ID,
		&record.ForwardRecordID,
		&record.CallbackRecordID,
		&record.UserID,
		&record.RetryAttempt,
		&record.ScheduledAt,
		&record.ExecutedAt,
		&record.Status,
		&record.ErrorMessage,
		&record.HTTPStatus,
		&record.ResponseData,
		&record.ExecutionDurationMs,
		&record.NextRetryAt,
		&record.CreatedAt,
		&record.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("重试记录不存在")
		}
		return nil, fmt.Errorf("获取重试记录失败: %w", err)
	}

	return record, nil
}

// UpdateRetryRecord 更新重试记录
func (r *PostgresCallbackRepository) UpdateRetryRecord(ctx context.Context, record *model.CallbackRetryRecord) error {
	query := `
		UPDATE callback_retry_records
		SET executed_at = $2, status = $3, error_message = $4, http_status = $5,
			response_data = $6, execution_duration_ms = $7, next_retry_at = $8, updated_at = $9
		WHERE id = $1
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ID,
		record.ExecutedAt,
		record.Status,
		record.ErrorMessage,
		record.HTTPStatus,
		record.ResponseData,
		record.ExecutionDurationMs,
		record.NextRetryAt,
		record.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("更新重试记录失败: %w", err)
	}

	return nil
}

// GetPendingRetryRecords 获取待重试记录
func (r *PostgresCallbackRepository) GetPendingRetryRecords(ctx context.Context, limit int) ([]*model.CallbackRetryRecord, error) {
	query := `
		SELECT id, forward_record_id, callback_record_id, user_id, retry_attempt,
			   scheduled_at, executed_at, status, error_message, http_status,
			   response_data, execution_duration_ms, next_retry_at, created_at, updated_at
		FROM callback_retry_records
		WHERE status = 'pending' AND scheduled_at <= NOW()
		ORDER BY scheduled_at ASC
		LIMIT $1
	`

	rows, err := r.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("查询待重试记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.CallbackRetryRecord
	for rows.Next() {
		record := &model.CallbackRetryRecord{}
		err := rows.Scan(
			&record.ID,
			&record.ForwardRecordID,
			&record.CallbackRecordID,
			&record.UserID,
			&record.RetryAttempt,
			&record.ScheduledAt,
			&record.ExecutedAt,
			&record.Status,
			&record.ErrorMessage,
			&record.HTTPStatus,
			&record.ResponseData,
			&record.ExecutionDurationMs,
			&record.NextRetryAt,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描重试记录失败: %w", err)
		}
		records = append(records, record)
	}

	return records, nil
}

// GetRetryRecordsByForwardRecordID 根据转发记录ID获取重试记录
func (r *PostgresCallbackRepository) GetRetryRecordsByForwardRecordID(ctx context.Context, forwardRecordID uuid.UUID) ([]*model.CallbackRetryRecord, error) {
	query := `
		SELECT id, forward_record_id, callback_record_id, user_id, retry_attempt,
			   scheduled_at, executed_at, status, error_message, http_status,
			   response_data, execution_duration_ms, next_retry_at, created_at, updated_at
		FROM callback_retry_records
		WHERE forward_record_id = $1
		ORDER BY retry_attempt ASC
	`

	rows, err := r.db.QueryContext(ctx, query, forwardRecordID)
	if err != nil {
		return nil, fmt.Errorf("查询重试记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.CallbackRetryRecord
	for rows.Next() {
		record := &model.CallbackRetryRecord{}
		err := rows.Scan(
			&record.ID,
			&record.ForwardRecordID,
			&record.CallbackRecordID,
			&record.UserID,
			&record.RetryAttempt,
			&record.ScheduledAt,
			&record.ExecutedAt,
			&record.Status,
			&record.ErrorMessage,
			&record.HTTPStatus,
			&record.ResponseData,
			&record.ExecutionDurationMs,
			&record.NextRetryAt,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描重试记录失败: %w", err)
		}
		records = append(records, record)
	}

	return records, nil
}

// GetRetryRecordsByUserID 根据用户ID获取重试记录
func (r *PostgresCallbackRepository) GetRetryRecordsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.CallbackRetryRecord, error) {
	query := `
		SELECT id, forward_record_id, callback_record_id, user_id, retry_attempt,
			   scheduled_at, executed_at, status, error_message, http_status,
			   response_data, execution_duration_ms, next_retry_at, created_at, updated_at
		FROM callback_retry_records
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询用户重试记录失败: %w", err)
	}
	defer rows.Close()

	var records []*model.CallbackRetryRecord
	for rows.Next() {
		record := &model.CallbackRetryRecord{}
		err := rows.Scan(
			&record.ID,
			&record.ForwardRecordID,
			&record.CallbackRecordID,
			&record.UserID,
			&record.RetryAttempt,
			&record.ScheduledAt,
			&record.ExecutedAt,
			&record.Status,
			&record.ErrorMessage,
			&record.HTTPStatus,
			&record.ResponseData,
			&record.ExecutionDurationMs,
			&record.NextRetryAt,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描重试记录失败: %w", err)
		}
		records = append(records, record)
	}

	return records, nil
}

// GetRetryRecordsCount 获取用户重试记录总数
func (r *PostgresCallbackRepository) GetRetryRecordsCount(ctx context.Context, userID string) (int64, error) {
	query := `SELECT COUNT(*) FROM callback_retry_records WHERE user_id = $1`

	var count int64
	err := r.db.QueryRowContext(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("获取重试记录总数失败: %w", err)
	}

	return count, nil
}

// CancelPendingRetryRecords 取消待重试记录
func (r *PostgresCallbackRepository) CancelPendingRetryRecords(ctx context.Context, forwardRecordID uuid.UUID) error {
	query := `
		UPDATE callback_retry_records
		SET status = 'cancelled', updated_at = NOW()
		WHERE forward_record_id = $1 AND status = 'pending'
	`

	_, err := r.db.ExecContext(ctx, query, forwardRecordID)
	if err != nil {
		return fmt.Errorf("取消待重试记录失败: %w", err)
	}

	return nil
}

// 🔥 新增：游标分页查询（最高性能）
func (r *PostgresCallbackRepository) GetEnhancedForwardRecordsWithCursor(ctx context.Context, userID string, limit int, cursor string, filters map[string]string) ([]*model.EnhancedForwardRecord, string, error) {
	// 🔥 游标分页优化策略：
	// 1. 使用created_at作为游标字段，避免OFFSET的性能问题
	// 2. 分别查询两个表，然后在应用层合并
	// 3. 返回下一页的游标

	var allRecords []*model.EnhancedForwardRecord
	var cursorTime time.Time
	var err error

	// 解析游标
	if cursor != "" {
		cursorTime, err = time.Parse(time.RFC3339, cursor)
		if err != nil {
			return nil, "", fmt.Errorf("无效的游标格式: %w", err)
		}
	} else {
		// 如果没有游标，使用当前时间
		cursorTime = time.Now()
	}

	// 检查是否有运单号筛选
	trackingNo := filters["tracking_no"]
	isTrackingNoFilter := trackingNo != ""

	// 🔥 优化1：如果有运单号筛选，优先使用精确匹配
	if isTrackingNoFilter {
		exactRecords, nextCursor, err := r.getCursorRecordsByExactTrackingNo(ctx, userID, trackingNo, limit, cursorTime, filters)
		if err != nil {
			return nil, "", fmt.Errorf("精确匹配运单号游标查询失败: %w", err)
		}

		// 如果精确匹配有结果，直接返回
		if len(exactRecords) > 0 {
			return exactRecords, nextCursor, nil
		}
	}

	// 🔥 优化2：分别查询两个表
	// 查询callback_forward_records
	callbackRecords, err := r.getCallbackForwardRecordsWithCursor(ctx, userID, limit*2, cursorTime, filters)
	if err != nil {
		return nil, "", fmt.Errorf("查询回调转发记录失败: %w", err)
	}
	allRecords = append(allRecords, callbackRecords...)

	// 查询work_order_forward_records
	workOrderRecords, err := r.getWorkOrderForwardRecordsWithCursor(ctx, userID, limit*2, cursorTime, filters)
	if err != nil {
		return nil, "", fmt.Errorf("查询工单转发记录失败: %w", err)
	}
	allRecords = append(allRecords, workOrderRecords...)

	// 🔥 优化3：在应用层排序和分页
	sort.Slice(allRecords, func(i, j int) bool {
		return allRecords[i].CreatedAt.After(allRecords[j].CreatedAt)
	})

	// 应用分页限制
	if len(allRecords) > limit {
		allRecords = allRecords[:limit]
	}

	// 生成下一页游标
	var nextCursor string
	if len(allRecords) == limit && len(allRecords) > 0 {
		// 使用最后一条记录的创建时间作为下一页游标
		nextCursor = allRecords[len(allRecords)-1].CreatedAt.Format(time.RFC3339)
	}

	return allRecords, nextCursor, nil
}

// 🔥 新增：精确匹配运单号的游标查询
func (r *PostgresCallbackRepository) getCursorRecordsByExactTrackingNo(ctx context.Context, userID, trackingNo string, limit int, cursorTime time.Time, filters map[string]string) ([]*model.EnhancedForwardRecord, string, error) {
	var records []*model.EnhancedForwardRecord

	// 查询callback_forward_records中的精确匹配
	query1 := `
		SELECT
			cfr.id, cfr.callback_url, cfr.request_data, cfr.response_data,
			cfr.http_status, cfr.status, cfr.retry_count, cfr.error_message,
			cfr.request_at, cfr.response_at, cfr.created_at,
			COALESCE(ucr.event_type, '') AS event_type,
			COALESCE(ucr.order_no, '') AS order_no,
			COALESCE(ucr.customer_order_no, '') AS customer_order_no,
			COALESCE(ucr.tracking_no, '') AS tracking_no
		FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1 AND ucr.tracking_no = $2 AND cfr.created_at < $3`

	args1 := []interface{}{userID, trackingNo, cursorTime}
	argIndex := 4

	if eventType := filters["event_type"]; eventType != "" {
		query1 += fmt.Sprintf(" AND ucr.event_type = $%d", argIndex)
		args1 = append(args1, eventType)
		argIndex++
	}

	if status := filters["status"]; status != "" {
		query1 += fmt.Sprintf(" AND cfr.status = $%d", argIndex)
		args1 = append(args1, status)
		argIndex++
	}

	query1 += fmt.Sprintf(" ORDER BY cfr.created_at DESC LIMIT $%d", argIndex)
	args1 = append(args1, limit)

	rows1, err := r.db.QueryContext(ctx, query1, args1...)
	if err != nil {
		return nil, "", err
	}
	defer rows1.Close()

	for rows1.Next() {
		var record model.EnhancedForwardRecord
		err := rows1.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, "", err
		}
		records = append(records, &record)
	}

	// 查询work_order_forward_records中的精确匹配
	query2 := `
		SELECT
			wfr.id, wfr.callback_url, wfr.request_data, wfr.response_data,
			wfr.http_status, wfr.status, wfr.retry_count, wfr.error_message,
			wfr.request_at, wfr.response_at, wfr.created_at,
			wfr.event_type AS event_type,
			COALESCE(wo.order_no, '') AS order_no,
			COALESCE(wo.order_no, '') AS customer_order_no,
			COALESCE(wo.tracking_no, '') AS tracking_no
		FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1 AND wo.tracking_no = $2 AND wfr.created_at < $3`

	args2 := []interface{}{userID, trackingNo, cursorTime}
	argIndex = 4

	if eventType := filters["event_type"]; eventType != "" {
		query2 += fmt.Sprintf(" AND wfr.event_type = $%d", argIndex)
		args2 = append(args2, eventType)
		argIndex++
	}

	if status := filters["status"]; status != "" {
		query2 += fmt.Sprintf(" AND wfr.status = $%d", argIndex)
		args2 = append(args2, status)
		argIndex++
	}

	query2 += fmt.Sprintf(" ORDER BY wfr.created_at DESC LIMIT $%d", argIndex)
	args2 = append(args2, limit)

	rows2, err := r.db.QueryContext(ctx, query2, args2...)
	if err != nil {
		return nil, "", err
	}
	defer rows2.Close()

	for rows2.Next() {
		var record model.EnhancedForwardRecord
		err := rows2.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, "", err
		}
		records = append(records, &record)
	}

	// 按创建时间排序
	sort.Slice(records, func(i, j int) bool {
		return records[i].CreatedAt.After(records[j].CreatedAt)
	})

	// 应用分页限制
	if len(records) > limit {
		records = records[:limit]
	}

	// 生成下一页游标
	var nextCursor string
	if len(records) == limit && len(records) > 0 {
		nextCursor = records[len(records)-1].CreatedAt.Format(time.RFC3339)
	}

	return records, nextCursor, nil
}

// 🔥 新增：回调转发记录的游标查询
func (r *PostgresCallbackRepository) getCallbackForwardRecordsWithCursor(ctx context.Context, userID string, limit int, cursorTime time.Time, filters map[string]string) ([]*model.EnhancedForwardRecord, error) {
	query := `
		SELECT
			cfr.id, cfr.callback_url, cfr.request_data, cfr.response_data,
			cfr.http_status, cfr.status, cfr.retry_count, cfr.error_message,
			cfr.request_at, cfr.response_at, cfr.created_at,
			COALESCE(ucr.event_type, '') AS event_type,
			COALESCE(ucr.order_no, '') AS order_no,
			COALESCE(ucr.customer_order_no, '') AS customer_order_no,
			COALESCE(ucr.tracking_no, '') AS tracking_no
		FROM callback_forward_records cfr
		LEFT JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE cfr.user_id = $1 AND cfr.created_at < $2`

	args := []interface{}{userID, cursorTime}
	argIndex := 3

	// 运单号筛选
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		query += fmt.Sprintf(" AND ucr.tracking_no ILIKE $%d", argIndex)
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		query += fmt.Sprintf(" AND ucr.event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		query += fmt.Sprintf(" AND cfr.status = $%d", argIndex)
		args = append(args, status)
		argIndex++
	}

	query += fmt.Sprintf(" ORDER BY cfr.created_at DESC LIMIT $%d", argIndex)
	args = append(args, limit)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var records []*model.EnhancedForwardRecord
	for rows.Next() {
		var record model.EnhancedForwardRecord
		err := rows.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, err
		}
		records = append(records, &record)
	}

	return records, nil
}

// 🔥 新增：工单转发记录的游标查询
func (r *PostgresCallbackRepository) getWorkOrderForwardRecordsWithCursor(ctx context.Context, userID string, limit int, cursorTime time.Time, filters map[string]string) ([]*model.EnhancedForwardRecord, error) {
	query := `
		SELECT
			wfr.id, wfr.callback_url, wfr.request_data, wfr.response_data,
			wfr.http_status, wfr.status, wfr.retry_count, wfr.error_message,
			wfr.request_at, wfr.response_at, wfr.created_at,
			wfr.event_type AS event_type,
			COALESCE(wo.order_no, '') AS order_no,
			COALESCE(wo.order_no, '') AS customer_order_no,
			COALESCE(wo.tracking_no, '') AS tracking_no
		FROM work_order_forward_records wfr
		LEFT JOIN work_orders wo ON wfr.work_order_id = wo.id
		WHERE wfr.user_id = $1 AND wfr.created_at < $2`

	args := []interface{}{userID, cursorTime}
	argIndex := 3

	// 运单号筛选
	if trackingNo := filters["tracking_no"]; trackingNo != "" {
		query += fmt.Sprintf(" AND wo.tracking_no ILIKE $%d", argIndex)
		args = append(args, "%"+trackingNo+"%")
		argIndex++
	}

	// 事件类型筛选
	if eventType := filters["event_type"]; eventType != "" {
		query += fmt.Sprintf(" AND wfr.event_type = $%d", argIndex)
		args = append(args, eventType)
		argIndex++
	}

	// 状态筛选
	if status := filters["status"]; status != "" {
		query += fmt.Sprintf(" AND wfr.status = $%d", argIndex)
		args = append(args, status)
		argIndex++
	}

	query += fmt.Sprintf(" ORDER BY wfr.created_at DESC LIMIT $%d", argIndex)
	args = append(args, limit)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var records []*model.EnhancedForwardRecord
	for rows.Next() {
		var record model.EnhancedForwardRecord
		err := rows.Scan(
			&record.ID, &record.CallbackURL, &record.RequestData, &record.ResponseData,
			&record.HTTPStatus, &record.Status, &record.RetryCount, &record.ErrorMessage,
			&record.RequestAt, &record.ResponseAt, &record.CreatedAt,
			&record.EventType, &record.OrderNo, &record.CustomerOrderNo, &record.TrackingNo,
		)
		if err != nil {
			return nil, err
		}
		records = append(records, &record)
	}

	return records, nil
}
